import type { SVGProps } from "react";
const SvgCode = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 20 20"
        {...props}
    >
        <path
            fill="#000"
            d="M18.334 1.666a.833.833 0 0 1 .833.833v15a.834.834 0 0 1-.834.834H1.668a.833.833 0 0 1-.834-.834v-15a.833.833 0 0 1 .834-.833zM17.5 3.333h-15v13.333h15zM10.277 5.42l.74.083a.416.416 0 0 1 .367.47l-1.071 7.83a.417.417 0 0 1-.459.358l-.74-.082a.416.416 0 0 1-.366-.47l1.07-7.831a.417.417 0 0 1 .46-.358M7.121 7.483l.522.485a.417.417 0 0 1 .015.596l-1.438 1.47 1.438 1.47a.417.417 0 0 1-.007.59l-.008.007-.522.485a.416.416 0 0 1-.58-.012L4.31 10.328a.417.417 0 0 1 0-.587L6.54 7.494a.417.417 0 0 1 .58-.011m6.338.011 2.232 2.247a.417.417 0 0 1 0 .587l-2.232 2.246a.416.416 0 0 1-.58.012l-.522-.485a.42.42 0 0 1-.014-.597l1.437-1.47-1.437-1.47a.417.417 0 0 1 .014-.596l.523-.485a.417.417 0 0 1 .579.011"
        />
    </svg>
);
export default SvgCode;

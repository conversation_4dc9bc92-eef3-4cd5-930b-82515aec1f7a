import styled from '@emotion/styled';
import {Flex, Form} from 'antd';
import {useCallback, useState} from 'react';
import {useDebouncedCallback} from 'huse';
import SquireFilter, {FilterValues, TabValues} from './SquireFilter';
import SquirePanel from './SquirePanel';
import RegionNavigation from './RegionNavigation';

const Container = styled(Flex)`
    width: 100%;
    height: calc(100vh - 48px);
    padding: 0 20px 16px;
    overflow: auto;
`;

const initialTabFormValue = {tab: 'all'};
const initialFilterFormValue: FilterValues = {
    serverSourceType: 'all',
    serverProtocolType: 'all',
    labels: [-2],
    viewOrder: 'DESC',
};
const initialSearchParams = {
    ...initialTabFormValue,
    ...initialFilterFormValue,
};

const MCPSquare = () => {
    const [searchParams, setSearchParams] = useState<FilterValues & TabValues>(initialSearchParams);
    const onFormChange = useCallback(
        (formName: string, info: {changedFields: any}) => {
            // 这里响应的是用户的修改，只取第一个就可以了。用户也没法一次改多个
            const changedParams: Partial<FilterValues & TabValues> = {
                [info.changedFields[0].name]: info.changedFields[0].value,
            };
            // 这俩互斥
            if (info.changedFields[0].name[0] === 'viewOrder') {
                changedParams.publishOrder = undefined;
            } else if (info.changedFields[0].name[0] === 'publishOrder') {
                changedParams.viewOrder = undefined;
            }
            setSearchParams(params => {
                return {
                    ...params,
                    ...changedParams,
                };
            });
        },
        [setSearchParams]
    );
    const onFormChangeWithDebounce = useDebouncedCallback(onFormChange, 200);
    return (
        <Container vertical gap={16}>
            <RegionNavigation />
            <Form.Provider onFormChange={onFormChangeWithDebounce}>
                <SquireFilter
                    initialTabFormValue={initialTabFormValue}
                    initialFilterFormValue={initialFilterFormValue}
                />
            </Form.Provider>
            <SquirePanel searchParams={searchParams} />
        </Container>
    );
};

export default MCPSquare;
